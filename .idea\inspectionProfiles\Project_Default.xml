<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="82">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="tqdm" />
            <item index="2" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="3" class="java.lang.String" itemvalue="torchcontrib" />
            <item index="4" class="java.lang.String" itemvalue="torch" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="segmentation_models_pytorch" />
            <item index="7" class="java.lang.String" itemvalue="torchvision" />
            <item index="8" class="java.lang.String" itemvalue="Pillow" />
            <item index="9" class="java.lang.String" itemvalue="scikit-image" />
            <item index="10" class="java.lang.String" itemvalue="scipy" />
            <item index="11" class="java.lang.String" itemvalue="dataclasses" />
            <item index="12" class="java.lang.String" itemvalue="tifffile" />
            <item index="13" class="java.lang.String" itemvalue="timm" />
            <item index="14" class="java.lang.String" itemvalue="yapf" />
            <item index="15" class="java.lang.String" itemvalue="perceptual" />
            <item index="16" class="java.lang.String" itemvalue="pillow" />
            <item index="17" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="18" class="java.lang.String" itemvalue="addict" />
            <item index="19" class="java.lang.String" itemvalue="h5py" />
            <item index="20" class="java.lang.String" itemvalue="ml-collections" />
            <item index="21" class="java.lang.String" itemvalue="pyyaml" />
            <item index="22" class="java.lang.String" itemvalue="foolbox" />
            <item index="23" class="java.lang.String" itemvalue="absl-py" />
            <item index="24" class="java.lang.String" itemvalue="google-pasta" />
            <item index="25" class="java.lang.String" itemvalue="protobuf" />
            <item index="26" class="java.lang.String" itemvalue="tensorflow-estimator" />
            <item index="27" class="java.lang.String" itemvalue="pygame" />
            <item index="28" class="java.lang.String" itemvalue="opt-einsum" />
            <item index="29" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="30" class="java.lang.String" itemvalue="cycler" />
            <item index="31" class="java.lang.String" itemvalue="gast" />
            <item index="32" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="33" class="java.lang.String" itemvalue="pyasn1-modules" />
            <item index="34" class="java.lang.String" itemvalue="oauthlib" />
            <item index="35" class="java.lang.String" itemvalue="astunparse" />
            <item index="36" class="java.lang.String" itemvalue="keras" />
            <item index="37" class="java.lang.String" itemvalue="pyparsing" />
            <item index="38" class="java.lang.String" itemvalue="Markdown" />
            <item index="39" class="java.lang.String" itemvalue="libclang" />
            <item index="40" class="java.lang.String" itemvalue="geotorch" />
            <item index="41" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="42" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="43" class="java.lang.String" itemvalue="wrapt" />
            <item index="44" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="45" class="java.lang.String" itemvalue="contourpy" />
            <item index="46" class="java.lang.String" itemvalue="fonttools" />
            <item index="47" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="48" class="java.lang.String" itemvalue="tensorboard" />
            <item index="49" class="java.lang.String" itemvalue="imageio" />
            <item index="50" class="java.lang.String" itemvalue="matplotlib" />
            <item index="51" class="java.lang.String" itemvalue="rsa" />
            <item index="52" class="java.lang.String" itemvalue="networkx" />
            <item index="53" class="java.lang.String" itemvalue="torchdiffeq" />
            <item index="54" class="java.lang.String" itemvalue="pyasn1" />
            <item index="55" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="56" class="java.lang.String" itemvalue="Jinja2" />
            <item index="57" class="java.lang.String" itemvalue="requests-oauthlib" />
            <item index="58" class="java.lang.String" itemvalue="Keras-Preprocessing" />
            <item index="59" class="java.lang.String" itemvalue="tensorflow" />
            <item index="60" class="java.lang.String" itemvalue="tensorboard-plugin-wit" />
            <item index="61" class="java.lang.String" itemvalue="PyWavelets" />
            <item index="62" class="java.lang.String" itemvalue="zipp" />
            <item index="63" class="java.lang.String" itemvalue="urllib3" />
            <item index="64" class="java.lang.String" itemvalue="six" />
            <item index="65" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="66" class="java.lang.String" itemvalue="opencv-python" />
            <item index="67" class="java.lang.String" itemvalue="packaging" />
            <item index="68" class="java.lang.String" itemvalue="tensorflow-io-gcs-filesystem" />
            <item index="69" class="java.lang.String" itemvalue="termcolor" />
            <item index="70" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="71" class="java.lang.String" itemvalue="cachetools" />
            <item index="72" class="java.lang.String" itemvalue="grpcio" />
            <item index="73" class="java.lang.String" itemvalue="pytz" />
            <item index="74" class="java.lang.String" itemvalue="google-auth" />
            <item index="75" class="java.lang.String" itemvalue="advertorch" />
            <item index="76" class="java.lang.String" itemvalue="PIL" />
            <item index="77" class="java.lang.String" itemvalue="ast" />
            <item index="78" class="java.lang.String" itemvalue="cma" />
            <item index="79" class="java.lang.String" itemvalue="argparse" />
            <item index="80" class="java.lang.String" itemvalue="thop" />
            <item index="81" class="java.lang.String" itemvalue="torchsummary" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>