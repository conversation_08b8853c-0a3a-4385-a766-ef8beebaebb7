import torch
import torch.nn as nn

class SimpleTextEncoder(nn.Module):
    """
    Simple text encoder for traditional text features (non-BERT)
    """
    def __init__(self, input_dim=300, output_dim=768):
        super(SimpleTextEncoder, self).__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # Simple linear transformation to match expected output dimension
        self.linear = nn.Linear(input_dim, output_dim)
        self.dropout = nn.Dropout(0.1)
        self.layer_norm = nn.LayerNorm(output_dim)
        
    def forward(self, text_features):
        """
        Args:
            text_features: Tensor of shape (batch_size, seq_len, input_dim)
        Returns:
            Tensor of shape (batch_size, seq_len, output_dim)
        """
        # Apply linear transformation
        output = self.linear(text_features)
        output = self.dropout(output)
        output = self.layer_norm(output)
        
        return output
