import pickle
import numpy as np

def check_data_structure(file_path):
    print(f"Checking data structure in: {file_path}")
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        
        print("Top-level keys:", list(data.keys()))
        
        for key in data.keys():
            print(f"\n=== {key} ===")
            if isinstance(data[key], dict):
                print(f"  Sub-keys: {list(data[key].keys())}")
                for sub_key in data[key].keys():
                    if isinstance(data[key][sub_key], np.ndarray):
                        print(f"    {sub_key}: shape {data[key][sub_key].shape}, dtype {data[key][sub_key].dtype}")
                    else:
                        print(f"    {sub_key}: type {type(data[key][sub_key])}")
            else:
                print(f"  Type: {type(data[key])}")
                if isinstance(data[key], np.ndarray):
                    print(f"  Shape: {data[key].shape}")
                    
    except Exception as e:
        print(f"Error loading file: {e}")

if __name__ == "__main__":
    # Check MOSI data
    mosi_path = "F:/project/项目27-多模态情感识别项目/MMSA-6/data/mosi_raw.pkl"
    check_data_structure(mosi_path)

    print("\n" + "="*50 + "\n")

    # Check MOSEI data
    mosei_path = "F:/project/项目27-多模态情感识别项目/MMSA-6/data/mosei_raw.pkl"
    try:
        check_data_structure(mosei_path)
    except Exception as e:
        print(f"Could not load MOSEI data: {e}")
